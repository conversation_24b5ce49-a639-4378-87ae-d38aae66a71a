/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package resourceScan

import (
	"github.com/gorilla/mux"
)

type ScanningResultRouter interface {
	InitScanningResultRouter(configRouter *mux.Router)
}

type ScanningResultRouterImpl struct {
	ScanningResultRestHandler ScanningResultRestHandler
}

func NewScanningResultRouterImpl(ScanningResultRestHandler ScanningResultRestHandler) *ScanningResultRouterImpl {
	return &ScanningResultRouterImpl{ScanningResultRestHandler: ScanningResultRestHandler}
}

func (router *ScanningResultRouterImpl) InitScanningResultRouter(configRouter *mux.Router) {
	configRouter.Path("").HandlerFunc(router.ScanningResultRestHandler.ScanResults).Methods("GET")
}
