/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// ReleaseAndInstalledAppInfo struct for ReleaseAndInstalledAppInfo
type ReleaseAndInstalledAppInfo struct {
	ReleaseInfo *ReleaseInfo `json:"releaseInfo,omitempty"`
	InstalledAppInfo *InstalledAppInfo `json:"installedAppInfo,omitempty"`
}

// NewReleaseAndInstalledAppInfo instantiates a new ReleaseAndInstalledAppInfo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewReleaseAndInstalledAppInfo() *ReleaseAndInstalledAppInfo {
	this := ReleaseAndInstalledAppInfo{}
	return &this
}

// NewReleaseAndInstalledAppInfoWithDefaults instantiates a new ReleaseAndInstalledAppInfo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewReleaseAndInstalledAppInfoWithDefaults() *ReleaseAndInstalledAppInfo {
	this := ReleaseAndInstalledAppInfo{}
	return &this
}

// GetReleaseInfo returns the ReleaseInfo field value if set, zero value otherwise.
func (o *ReleaseAndInstalledAppInfo) GetReleaseInfo() ReleaseInfo {
	if o == nil || o.ReleaseInfo == nil {
		var ret ReleaseInfo
		return ret
	}
	return *o.ReleaseInfo
}

// GetReleaseInfoOk returns a tuple with the ReleaseInfo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ReleaseAndInstalledAppInfo) GetReleaseInfoOk() (*ReleaseInfo, bool) {
	if o == nil || o.ReleaseInfo == nil {
		return nil, false
	}
	return o.ReleaseInfo, true
}

// HasReleaseInfo returns a boolean if a field has been set.
func (o *ReleaseAndInstalledAppInfo) HasReleaseInfo() bool {
	if o != nil && o.ReleaseInfo != nil {
		return true
	}

	return false
}

// SetReleaseInfo gets a reference to the given ReleaseInfo and assigns it to the ReleaseInfo field.
func (o *ReleaseAndInstalledAppInfo) SetReleaseInfo(v ReleaseInfo) {
	o.ReleaseInfo = &v
}

// GetInstalledAppInfo returns the InstalledAppInfo field value if set, zero value otherwise.
func (o *ReleaseAndInstalledAppInfo) GetInstalledAppInfo() InstalledAppInfo {
	if o == nil || o.InstalledAppInfo == nil {
		var ret InstalledAppInfo
		return ret
	}
	return *o.InstalledAppInfo
}

// GetInstalledAppInfoOk returns a tuple with the InstalledAppInfo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ReleaseAndInstalledAppInfo) GetInstalledAppInfoOk() (*InstalledAppInfo, bool) {
	if o == nil || o.InstalledAppInfo == nil {
		return nil, false
	}
	return o.InstalledAppInfo, true
}

// HasInstalledAppInfo returns a boolean if a field has been set.
func (o *ReleaseAndInstalledAppInfo) HasInstalledAppInfo() bool {
	if o != nil && o.InstalledAppInfo != nil {
		return true
	}

	return false
}

// SetInstalledAppInfo gets a reference to the given InstalledAppInfo and assigns it to the InstalledAppInfo field.
func (o *ReleaseAndInstalledAppInfo) SetInstalledAppInfo(v InstalledAppInfo) {
	o.InstalledAppInfo = &v
}

func (o ReleaseAndInstalledAppInfo) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ReleaseInfo != nil {
		toSerialize["releaseInfo"] = o.ReleaseInfo
	}
	if o.InstalledAppInfo != nil {
		toSerialize["installedAppInfo"] = o.InstalledAppInfo
	}
	return json.Marshal(toSerialize)
}

type NullableReleaseAndInstalledAppInfo struct {
	value *ReleaseAndInstalledAppInfo
	isSet bool
}

func (v NullableReleaseAndInstalledAppInfo) Get() *ReleaseAndInstalledAppInfo {
	return v.value
}

func (v *NullableReleaseAndInstalledAppInfo) Set(val *ReleaseAndInstalledAppInfo) {
	v.value = val
	v.isSet = true
}

func (v NullableReleaseAndInstalledAppInfo) IsSet() bool {
	return v.isSet
}

func (v *NullableReleaseAndInstalledAppInfo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableReleaseAndInstalledAppInfo(val *ReleaseAndInstalledAppInfo) *NullableReleaseAndInstalledAppInfo {
	return &NullableReleaseAndInstalledAppInfo{value: val, isSet: true}
}

func (v NullableReleaseAndInstalledAppInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableReleaseAndInstalledAppInfo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


