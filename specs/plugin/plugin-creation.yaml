openapi: "3.0.3"
info:
  title: "Plugin Creation"
  description: |
    This API spec define changes in several APIs related to plugin, so that plugin creation feature is incorporated.
  version: "1.0.0"

paths:
  /orchestrator/plugin/global:
    post:
      description: create/update/delete a plugin(/plugin version)
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PluginsDto'
      responses:
        '200':
          description: successfully return the list of all plugins
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/PluginsDto'
        '400':
          description: Bad request, Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized, not found or invalid API token provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

        '403':
          description: Forbidden, user is not allowed to access this api
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref:  '#/components/schemas/Error'

  /orchestrator/plugin/global/list/versions/min:
    get:
      description: >
        Retrieves a min list of all plugins with all it's versions list and it's id.
      parameters:
        - name: appId
          description: to apply RBAC appId would be needed
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: successfully return the list of all plugins
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    items:
                      $ref: '#/components/schemas/PluginMinDto'
        '400':
          description: Bad request, Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized, not found or invalid API token provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

        '403':
          description: Forbidden, user is not allowed to access this api
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    PluginMinDto:
      type: object
      properties:
        pluginName:
          type: string
          description: name of plugin
        pluginVersions:
          type: array
          description: min version  detail with only version name and id
          items:
            $ref: '#/components/schemas/PluginVersionsMinDto'
    PluginVersionsMinDto:
      type: object
      properties:
        id:
          type: integer
          description: plugin version id
        version:
          type: string
          description: version of that plugin


    PluginsDto:
      type: object
      properties:
        parentPlugins:
          type: array
          items:
            $ref: '#/components/schemas/ParentPlugin'
        totalCount:
          type: integer
          description: this will tell the overall count of all plugins available

    ParentPlugin:
      type: object
      properties:
        id:
          type: integer
          description: this is the id of the parent plugin under which multiple plugin versions can exist, acts as a unique identifier of a plugin parent
            this id comes from plugin_parent_metadata table
        name:
          type: string
          description: name of the parent plugin under which multiple versions can exist
        identifier:
          type: string
          description: this is a special string identifier that uniquely identifies a plugin, would help a user to create a version of
            a plugin using a plugin identifier, instead of playing with plugin ids, for all the pre-existing plugins, identifier
            is saved by converting all the characters to lowercase and replacing all the spaces with underscore(-), if two plugin identifier collides
            then a pluginParentId is append at the last (eg. k6-load-testing-1)
        description:
          type: string
          description: this is the description of a plugin at parent level.
        type:
          type: string
          description: this can have two values PRESET or SHARED, if a user is creating a plugin for themselves, which is when plugin is created using
            the api then type should be SHARED, and all the system provided plugins are type=PRESET
        pluginVersions:
          $ref: '#/components/schemas/PluginVersions'
        icon:
          type: string
          description: this is the link to the plugin icon png to be displayed

    PluginVersions:
      type: object
      properties:
        detailedPluginVersionData:
          type: array
          description: contains detailed data with all input and output variables
          items:
            $ref: '#/components/schemas/PluginsVersionDetail'
        minimalPluginVersionData:
          type: array
          description: contains only few metadata excluding input and output variables
          items:
            $ref: '#/components/schemas/PluginsVersionDetail'


    PluginsVersionDetail:
      type: object
      properties:
        id:
          type: integer
          description: this is the id that corresponds to a particular plugin version, one can say that this is a plugin version's id, and one PluginParentMetadataDto.Id can have multiple versions hence multiple ids
            this id comes from plugin_metadata table
        name:
          type: string
          description: name of the plugin
        description:
          type: string
          description: this is the description of a plugin at version level.
        tags:
          type: array
          description: tags corresponding to a plugin version
          items:
            type: string
        action:
          type: string
          description: it defines what operation user wants to do for that plugin,
          enum: [Create, Update, Delete]
        pluginStage:
          type: string
          description: It tells at what stage of a workflow this plugin is being created, CI, CD or CI_CD
          enum: [CI, CD, CI_CD]
        newTags:
          type: array
          description: tags contained in this array are newly created tags by user.
          items:
            type: string
        pluginSteps:
          $ref: '#/components/schemas/PluginStepsDto'
        updatedBy:
          type: string
          description: plugin last updated by, this will store the email of the person who has created the latest plugin
        inputVariables:
          type: array
          items:
            $ref: '#/components/schemas/PluginVariableDto'
        outputVariables:
          type: array
          items:
            $ref: '#/components/schemas/PluginVariableDto'
        docLink:
          type: string
          description: this is the documentation link of a particular version of a plugin
        pluginVersion:
          type: string
          description: this is the version of a plugin. eg. 1.0.0, 1.1.0-betaV2 or 1.2.0+alpha
        isLatest:
          type: boolean
          description: this will tell if a plugin version is latest or not. isLatest would be calculated on the basis of which version is newly created.


    PluginStepsDto:
      type: object
      properties:
       id:
        type: integer
        description: Unique identifier of the plugin step.
       name:
        type: string
        description: Name of the plugin step.
       description:
        type: string
        description: Detailed description of what the plugin step does.
       index:
        type: integer
        description: The order index of the plugin step within the plugin.
       stepType:
        type: string
        enum: [ INLINE, REF_PLUGIN ]
        description: Type of the plugin step, indicating whether it's an INLINE step defined within the plugin or a REF_PLUGIN step referencing another plugin.
       refPluginId:
        type: integer
        description: Unique identifier of the plugin used as reference by this step.
       outputDirectoryPath:
        type: array
        items:
          type: string
        description: Paths to directories where the output of the plugin step should be stored.
       dependentOnStep:
        type: string
        description: Identifier of the step, this step depends on to run. It can be used to establish execution order.
       pluginStepVariable:
        type: array
        items:
          $ref: '#/components/schemas/PluginVariableDto'
        description: Optional. A list of variables associated with this plugin step.
       pluginPipelineScript:
        allOf:
          - $ref: '#/components/schemas/PluginPipelineScript'
          - description: Script associated with this plugin step to be executed as part of the pipeline. Optional.
      required:
      - name
      - description
      - index
      - stepType
      - refPluginId
      - outputDirectoryPath
      - dependentOnStep

    PluginVariableDto:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier of the plugin variable.
        name:
          type: string
          description: The name of the plugin variable.
        format:
          type: string
          description: The format of the variable value.
          enum:
            - STRING
            - NUMBER
            - BOOL
            - DATE
          example:
            - STRING
        description:
          type: string
          description: A description of the plugin variable.
        isExposed:
          type: boolean
          description: Indicates whether the variable is exposed.
        allowEmptyValue:
          type: boolean
          description: Indicates whether an empty value is allowed for the variable.
        defaultValue:
          type: string
          description: The default value of the variable.
        value:
          type: string
          description: The value of the variable.
        variableType:
          type: string
          description: |
            The type of the variable. This specifies whether the variable is required by the plugin (Marked as INPUT type) or whether that variable is produced by the plugin (Marked as OUTPUT type).
          enum:
            - OUTPUT
            - INPUT
          example:
            - INPUT
        valueType:
          type: string
          description: |
            The value type of the variable. Specifies whether the plugin uses a new value provided by the user (marked as NEW), retrieves the value from the previous step (marked as FROM_PREVIOUS_STEP), or fetches a global value (marked as GLOBAL).
            This indicates whether the plugin utilizes a new user-provided value, a value from a previous step, or a global value.
          enum: [NEW, FROM_PREVIOUS_STEP,GLOBAL]
          example:
            - NEW
            - FROM_PREVIOUS_STEP
            - GLOBAL
        previousStepIndex:
          type: integer
          description: The index of the previous step.
        variableStepIndex:
          type: integer
          description: The index of the variable step.
        variableStepIndexInPlugin:
          type: integer
          description: The index of the variable step in the plugin.
        referenceVariableName:
          type: string
          description: The name of the reference variable.
        pluginStepCondition:
          type: array
          items:
            allOf:
              - $ref: '#/components/schemas/PluginStepCondition'
              - description: The conditions associated with the plugin variable.
      required:
        - name
        - format
        - description
        - isExposed
        - allowEmptyValue
        - defaultValue
        - variableType
        - variableStepIndex
        - variableStepIndexInPlugin


    PluginStepCondition:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier of the plugin step condition.
        pluginStepId:
          type: integer
          description: The identifier of the plugin step associated with this condition.
        conditionVariableId:
          type: integer
          description: The identifier of the variable on which the condition is written.
        conditionType:
          type: string
          description: >
            The type of condition.
            Possible values are:
            - SKIP: Skips the plugin step.
            - TRIGGER: Triggers the plugin step.
            - SUCCESS: Executes the plugin step on success.
            - FAIL: Executes the plugin step on failure.
          enum:
            - SKIP
            - TRIGGER
            - SUCCESS
            - FAIL
          example: SKIP
        conditionalOperator:
          type: string
          description: The operator used in the condition.
        conditionalValue:
          type: string
          description: The value associated with the condition.
        deleted:
          type: boolean
          description: Specifies whether the condition is deleted.
      required:
        - pluginStepId
        - conditionVariableId
        - conditionType
        - conditionalOperator
        - conditionalValue
        - deleted

    PluginPipelineScript:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier of the plugin pipeline script. Even if it is skipped by the user it will automatically get created with the default value
        script:
          type: string
          description: The script associated with the plugin pipeline.
        storeScriptAt:
          type: string
          description: The location where the script is stored.
        type:
          type: string
          description: >
            Specifies the type of script.
            Possible values are:
             - SHELL: Shell script.
             - DOCKERFILE: Dockerfile script.
             - CONTAINER_IMAGE: Container image script.
          enum:
            - SHELL
            - DOCKERFILE
            - CONTAINER_IMAGE
          example:
            - SHELL
        dockerfileExists:
          type: boolean
          description: Indicates whether a Dockerfile exists for the script.
        mountPath:
          type: string
          description: The path where the script is mounted.
        mountCodeToContainer:
          type: boolean
          description: Indicates whether code is mounted to the container.
        mountCodeToContainerPath:
          type: string
          description: The path where code is mounted to the container.
        mountDirectoryFromHost:
          type: boolean
          description: Indicates whether a directory is mounted from the host.
        containerImagePath:
          type: string
          description: The path to the container image.
        imagePullSecretType:
          type: string
          description: >
            Specifies the type of image pull secret.
            Possible values are:
            - CONTAINER_REGISTRY: Container registry image pull secret.
            - SECRET_PATH: Secret path image pull secret.
          enum:
            - CONTAINER_REGISTRY
            - SECRET_PATH
          example:
            - CONTAINER_REGISTRY
            - SECRET_PATH

        imagePullSecret:
          type: string
          description: The image pull secret.
        deleted:
          type: boolean
          description: Indicates whether the plugin pipeline script is deleted.
        pathArgPortMapping:
          type: array
          items:
            $ref: '#/components/schemas/ScriptPathArgPortMapping'
          description: The path argument port mappings associated with the plugin pipeline script.
      required:
        - script
        - storeScriptAt
        - type
        - dockerfileExists
        - mountPath
        - mountCodeToContainer
        - mountCodeToContainerPath
        - mountDirectoryFromHost
        - containerImagePath
        - imagePullSecretType
        - imagePullSecret
        - deleted
        - pathArgPortMapping


    ScriptPathArgPortMapping:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier of the script path argument port mapping. Even if it is skipped by the user it will automatically get created with the default value
        typeOfMapping:
          type: string
          description: >
            Specifies the type of mapping.
            Possible values are:
            - FILE_PATH
            - DOCKER_ARG
            - PORT
          enum:
            - FILE_PATH
            - DOCKER_ARG
            - PORT
          example:
            - PORT
        filePathOnDisk:
          type: string
          description: The file path on the local disk.
        filePathOnContainer:
          type: string
          description: The file path on the container.
        command:
          type: string
          description: The command associated with the mapping.
        args:
          type: array
          items:
            type: string
          description: The arguments associated with the command.
        portOnLocal:
          type: integer
          description: The port on the local machine.
        portOnContainer:
          type: integer
          description: The port on the container.
        scriptId:
          type: integer
          description: The identifier of the script associated with the mapping.
      required:
        - id
        - typeOfMapping
        - filePathOnDisk
        - filePathOnContainer
        - command
        - args
        - portOnLocal
        - portOnContainer
        - scriptId

    Error:
      title: Error
      type: object
      description: "A general error schema returned when status is not 200 OK"
      properties:
        code:
          type: string
          description: "a code for this particular error"
        internalMessage:
          type: string
          description: "Optional. a message with further detail"
        userMessage:
          type: string
          description: "Optional. A message for the user"
        userDetailsMessage:
          type: string
          description: "Optional. Detailed User message"