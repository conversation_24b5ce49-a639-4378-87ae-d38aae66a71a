## v0.6.7

## Bugs
- fix: Lifecycle hook failing due to default values (#2671)
- fix: unblocked pre cd auto trigger in case of manual cd  (#2666)
- fix: app_id ambiguous fix (#2661)
- fix: checking git material active status for multiple places :  1) while triggering ci 2) while getting commit info 3) while getting ci pipeline (#2636)
- fix:updated ciBuildType check in app create (#2652)
## Enhancements
- feat: Audit logs for docker and git repositories (#2655)
## Documentation
- docs: devtron readme - credentials update (#2663)
- docs: Image Pull Secret Doc (#2604)
- docs: updated url for the license file (#2662)
## Others
- chore: bump beta release to stable (#2677)
- Ingress Urls User level access Issue (#2659)


## v0.6.7-rc.0

## Bugs
- Fix: user re create with super admin permission fix. (#2645)
- fix: sql scripts for replacing CronJob & Job chart name to Job & CronJob in tables (#2638)
- fix: changed the description for chart types, written update sql scripts (#2632)
- fix: corrected health url in auth whitelist list (#2629)
- fix: updated devtron-reference-chart directory (#2603)
## Enhancements
- feat: Exposed Devtron docker image in container as variable (#2639)
## Documentation
- doc: broken hyperlinks in doc fix (#2621)
- docs: fix in gcp installation and configuration command (#2614)
## Others
- chore: fixed release title for stable releases (#2626)
- chore: Updated stable to beta for nightly release (#2619)
- Docker checkout path Fix (#2616)
- feat : Auto inject Image pull secret while deploying devtron-app (#2547)
- Telemetry modifications (#2567)


