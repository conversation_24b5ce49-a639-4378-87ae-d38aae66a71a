name: sync

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  release:
    types: [released]
# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  sync:
    runs-on: ubuntu-latest
    name: Git Repo Sync
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - uses: devtron-labs/git-repo-sync@v0.1.9
      with:
        script-file: ${{ secrets.GITEE_SYNC_FILE }}
        target-url: ${{ secrets.TARGET_URL }}
        target-username: ${{ secrets.TARGET_USER_NAME }}
        target-token: ${{ secrets.GITEE_ACCESS_TOKEN }}
        target-useremail: ${{ secrets.TARGET_USER_EMAIL }}

