-- update notification templates for CI build for ses
UPDATE notification_templates
SET template_payload = '{
    "from": "{{fromEmail}}",
    "to": "{{toEmail}}",
    "subject": "Build pipeline triggered | Application: {{appName}} at {{eventTime}}",
    "html": "<table cellpadding=0 style=\"font-family:Arial,Verdana,Helvetica;width:600px;height:485px;border-collapse:inherit;border-spacing:0;border:1px solid #d0d4d9;border-radius:8px;padding:16px 20px;margin:20px auto;box-shadow:0 0 8px 0 rgba(0,0,0,.1)\"><tr><td colspan=3><div style=\"padding-bottom:16px;margin-bottom:20px;border-bottom:1px solid #edf1f5;max-width:600px\"><img src=https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/devtron/devtron-logo.png style=max-width:122px alt=cd-triggered></div><tr><td colspan=3><div style=\"background-color:#e5f2ff;border-radius:8px 8px 0 0;padding:20px;display:flex;justify-content:space-between\"><div style=width:90%><div style=font-size:16px;line-height:24px;font-weight:600;margin-bottom:6px;color:#000a14>▶️ Build pipeline triggered</div><span style=font-size:14px;line-height:20px;color:#000a14>{{eventTime}}</span><br><div><span style=font-size:14px;line-height:20px;color:#000a14>by</span><span style=font-size:14px;line-height:20px;color:#06c;margin-left:4px>{{triggeredBy}}</span></div></div><div><img src=https://cdn.devtron.ai/images/img-build-notification.png style=height:72px;width:72px></div></div><tr><td colspan=3><div style=display:flex><div style=\"width:124px;background-color:#e5f2ff;padding:0 0 20px 20px;border-bottom-left-radius:8px\">{{#buildHistoryLink}}<a href={{&buildHistoryLink}} style=\"height:32px;padding:7px 12px;line-height:32px;font-size:12px;font-weight:600;border-radius:4px;text-decoration:none;background:#06c;color:#fff;border:1px solid transparent;cursor:pointer;text-align:center\">View Pipeline</a>{{/buildHistoryLink}}</div><div style=\"width:90%;background-color:#e5f2ff;padding:0 0 20px 20px;border-bottom-right-radius:8px\"></div></div></tr><td></td><tr><td><br><tr><td><div style=color:#3b444c;font-size:13px>Application</div><td colspan=2><div style=color:#3b444c;font-size:13px>Pipeline</div><tr><td><div style=color:#000a14;font-size:14px>{{appName}}</div><td colspan=2><div style=color:#000a14;font-size:14px>{{pipelineName}}</div><tr><td colspan=3><div style=\"font-weight:600;margin-top:20px;width:100%;border-top:1px solid #edf1f5;padding:16px 0 12px;font-size:14px\">Source Code</div></tr>{{#ciMaterials}}{{^webhookType}}<tr><td><div style=color:#3b444c;font-size:13px>Branch</div><td colspan=2><div style=color:#3b444c;font-size:13px>Commit</div><tr><td><div style=color:#000a14;font-size:14px>{{appName}}/{{branch}}</div><td colspan=2><div style=color:#000a14;font-size:14px><a href=\"{{& commitLink }}\">{{commit}}</a></div></tr>{{/webhookType}} {{#webhookData.mergedType}}<tr><td><div style=color:#3b444c;font-size:13px>Title</div><td colspan=2><div style=color:#3b444c;font-size:13px>Git URL</div><tr><td><div style=color:#000a14;font-size:14px>{{webhookData.data.title}}</div><td colspan=2><div style=color:#000a14;font-size:14px><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.giturl}}</a></div><tr><td><div style=color:#3b444c;font-size:13px>Source Branch</div><td colspan=2><div style=color:#3b444c;font-size:13px>Source Commit</div><tr><td><div style=color:#000a14;font-size:14px>{{webhookData.data.sourcebranchname}}</div><td colspan=2><div style=color:#000a14;font-size:14px><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.sourcecheckout}}</a></div><tr><td><div style=color:#3b444c;font-size:13px>Target Branch</div><td colspan=2><div style=color:#3b444c;font-size:13px>Target Commit</div><tr><td><div style=color:#000a14;font-size:14px>{{webhookData.data.targetbranchname}}</div><td colspan=2><div style=color:#000a14;font-size:14px><a href=\"{{& webhookData.data.targetcheckoutlink}}\">{{webhookData.data.targetcheckout}}</a></div></tr>{{/webhookData.mergedType}}{{/ciMaterials}}<tr><td colspan=3><div style=\"border-top:1px solid #edf1f5;margin:20px 0 16px;height:1px\"></div><tr><td colspan=2 style=display:flex><a href=https://twitter.com/DevtronL style=cursor:pointer;text-decoration:none;padding-right:12px;display:flex target=_blank><img src=https://cdn.devtron.ai/images/twitter_social_dark.png style=width:20px></a><a href=https://www.linkedin.com/company/devtron-labs/mycompany/ style=cursor:pointer;text-decoration:none;padding-right:12px;display:flex target=_blank><img src=https://cdn.devtron.ai/images/linkedin_social_dark.png style=width:20px></a><a href=https://devtron.ai/blog/ style=color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline;padding-right:12px target=_blank>Blog</a><a href=https://devtron.ai/ style=color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline target=_blank>Website</a><td style=text-align:right><div style=color:#767d84;font-size:13px;line-height:20px>© Devtron Labs 2024</div></table>"}'
WHERE node_type = 'CI'
AND event_type_id = 1
AND channel_type='ses';



UPDATE notification_templates
SET template_payload = '{
    "from": "{{fromEmail}}",
    "to": "{{toEmail}}",
    "subject": "Build pipeline Succeeded | Application: {{appName}} at {{eventTime}}",
    "html": "<table cellpadding=\"0\" style=\"font-family:Arial,Verdana,Helvetica;width:600px;height:485px;border-collapse:inherit;border-spacing:0;border:1px solid #d0d4d9;border-radius:8px;padding:16px 20px;margin:20px auto;box-shadow:0 0 8px 0 rgba(0,0,0,.1);\"><tr><td colspan=\"3\"><div style=\"padding-bottom:16px;margin-bottom:20px;border-bottom:1px solid #edf1f5;max-width:600px;\"><img src=\"https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/devtron/devtron-logo.png\" style=\"max-width:122px;\" alt=\"cd-triggered\"></div></td></tr><tr><td colspan=\"3\"><div style=\"background-color:#E9FBF4;border-radius:8px 8px 0 0;padding:20px;display:flex;justify-content:space-between;\"><div style=\"width:90%;\"><div style=\"font-size:16px;line-height:24px;font-weight:600;margin-bottom:6px;color:#000a14;\">🎉 Build pipeline succeeded</div><span style=\"font-size:14px;line-height:20px;color:#000a14;\">{{eventTime}}</span><br><div><span style=\"font-size:14px;line-height:20px;color:#000a14;\">by</span><span style=\"font-size:14px;line-height:20px;color:#06c;margin-left:4px;\">{{triggeredBy}}</span></div></div><div><img src=\"https://cdn.devtron.ai/images/img-build-notification.png \" style=\"height:72px;width:72px;\"></div></div></td></tr><tr><td colspan=\"3\"><div style=\"display:flex;\"><div style=\"width:124px;background-color:#E9FBF4;padding:0 0 20px 20px;border-bottom-left-radius:8px;\">{{#buildHistoryLink}}<a href=\"{{&buildHistoryLink}}\" style=\"height:32px;padding:7px 12px;line-height:32px;font-size:12px;font-weight:600;border-radius:4px;text-decoration:none;background:#06c;color:#fff;border:1px solid transparent;cursor:pointer;text-align:center;\">View Pipeline</a>{{/buildHistoryLink}}</div><div style=\"width:90%;background-color:#E9FBF4;padding:0 0 20px 20px;border-bottom-right-radius:8px;\"></div></div></td></tr><td></td><tr><td><br></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px;\">Application</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px;\">Pipeline</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px;\">{{appName}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px;\">{{pipelineName}}</div></td></tr><tr><td colspan=\"3\"><div style=\"font-weight:600;margin-top:20px;width:100%;border-top:1px solid #edf1f5;padding:16px 0 12px;font-size:14px;\">Source Code</div></td></tr>{{#ciMaterials}}{{^webhookType}}<tr><td><div style=\"color:#3b444c;font-size:13px;\">Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px;\">Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px;\">{{appName}}/{{branch}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px;\"><a href=\"{{& commitLink }}\">{{commit}}</a></div></td></tr>{{#webhookData.mergedType}}<tr><td><div style=\"color:#3b444c;font-size:13px\">Title</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Git URL</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.title}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.giturl}}</a></div></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px\">Source Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Source Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.sourcebranchname}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.sourcecheckout}}</a></div></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px\">Target Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Target Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.targetbranchname}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.targetcheckoutlink}}\">{{webhookData.data.targetcheckout}}</a></div></td></tr>{{/webhookData.mergedType}}{{/webhookType}}{{/ciMaterials}}<tr><td colspan=\"3\"><div style=\"border-top:1px solid #edf1f5;margin:20px 0 16px;height:1px;\"></div></td></tr><tr><td colspan=\"2\" style=\"display:flex;\"><a href=\"https://twitter.com/DevtronL\" style=\"cursor:pointer;text-decoration:none;padding-right:12px;display:flex;\" target=\"_blank\"><img src=\"https://cdn.devtron.ai/images/twitter_social_dark.png\" style=\"width:20px;\"></a><a href=\"https://www.linkedin.com/company/devtron-labs/mycompany/\" style=\"cursor:pointer;text-decoration:none;padding-right:12px;display:flex;\" target=\"_blank\"><img src=\"https://cdn.devtron.ai/images/linkedin_social_dark.png\" style=\"width:20px;\"></a><a href=\"https://devtron.ai/blog/\" style=\"color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline;padding-right:12px;\" target=\"_blank\">Blog</a><a href=\"https://devtron.ai/\" style=\"color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline;\" target=\"_blank\">Website</a></td><td style=\"text-align:right;\"><div style=\"color:#767d84;font-size:13px;line-height:20px;\">© Devtron Labs 2024</div></td></tr></table>"}'
WHERE node_type = 'CI'
AND event_type_id = 2
AND channel_type='ses';



-- update notification template for Build failed ses with Webhooks
UPDATE notification_templates
SET template_payload = '{
    "from": "{{fromEmail}}",
    "to": "{{toEmail}}",
    "subject": "Build pipeline Failed | Application: {{appName}} at {{eventTime}}",
    "html": "<table cellpadding=\"0\" style=\"font-family:Arial,Verdana,Helvetica;width:600px;height:485px;border-collapse:inherit;border-spacing:0;border:1px solid #d0d4d9;border-radius:8px;padding:16px 20px;margin:20px auto;box-shadow:0 0 8px 0 rgba(0,0,0,.1);\"><tr><td colspan=\"3\"><div style=\"padding-bottom:16px;margin-bottom:20px;border-bottom:1px solid #edf1f5;max-width:600px;\"><img src=\"https://devtron-public-asset.s3.us-east-2.amazonaws.com/images/devtron/devtron-logo.png\" style=\"max-width:122px;\" alt=\"cd-triggered\"></div></td></tr><tr><td colspan=\"3\"><div style=\"background-color:#fde7e7;border-radius:8px 8px 0 0;padding:20px;display:flex;justify-content:space-between;\"><div style=\"width:90%;\"><div style=\"font-size:16px;line-height:24px;font-weight:600;margin-bottom:6px;color:#000a14;\">❌ Build pipeline failed</div><span style=\"font-size:14px;line-height:20px;color:#000a14;\">{{eventTime}}</span><br><div><span style=\"font-size:14px;line-height:20px;color:#000a14;\">by</span><span style=\"font-size:14px;line-height:20px;color:#06c;margin-left:4px;\">{{triggeredBy}}</span></div></div><div><img src=\"https://cdn.devtron.ai/images/img-build-notification.png \" style=\"height:72px;width:72px;\"></div></div></td></tr><tr><td colspan=\"3\"><div style=\"display:flex;\"><div style=\"width:124px;background-color:#fde7e7;padding:0 0 20px 20px;border-bottom-left-radius:8px;\">{{#buildHistoryLink}}<a href=\"{{&buildHistoryLink}}\" style=\"height:32px;padding:7px 12px;line-height:32px;font-size:12px;font-weight:600;border-radius:4px;text-decoration:none;background:#06c;color:#fff;border:1px solid transparent;cursor:pointer;text-align:center;\">View Pipeline</a>{{/buildHistoryLink}}</div><div style=\"width:90%;background-color:#fde7e7;padding:0 0 20px 20px;border-bottom-right-radius:8px;\"></div></div></td></tr><td></td><tr><td><br></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px;\">Application</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px;\">Pipeline</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px;\">{{appName}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px;\">{{pipelineName}}</div></td></tr><tr><td colspan=\"3\"><div style=\"font-weight:600;margin-top:20px;width:100%;border-top:1px solid #edf1f5;padding:16px 0 12px;font-size:14px;\">Source Code</div></td></tr>{{#ciMaterials}}{{^webhookType}}<tr><td><div style=\"color:#3b444c;font-size:13px;\">Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px;\">Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px;\">{{appName}}/{{branch}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px;\"><a href=\"{{& commitLink }}\">{{commit}}</a></div></td></tr>{{#webhookData.mergedType}}{{#webhookData.mergedType}}<tr><td><div style=\"color:#3b444c;font-size:13px\">Title</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Git URL</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.title}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.giturl}}</a></div></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px\">Source Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Source Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.sourcebranchname}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.sourcecheckoutlink}}\">{{webhookData.data.sourcecheckout}}</a></div></td></tr><tr><td><div style=\"color:#3b444c;font-size:13px\">Target Branch</div></td><td colspan=\"2\"><div style=\"color:#3b444c;font-size:13px\">Target Commit</div></td></tr><tr><td><div style=\"color:#000a14;font-size:14px\">{{webhookData.data.targetbranchname}}</div></td><td colspan=\"2\"><div style=\"color:#000a14;font-size:14px\"><a href=\"{{& webhookData.data.targetcheckoutlink}}\">{{webhookData.data.targetcheckout}}</a></div></td></tr>{{/webhookData.mergedType}}{{/webhookData.mergedType}}{{/webhookType}}{{/ciMaterials}}<tr><td colspan=\"3\"><div style=\"border-top:1px solid #edf1f5;margin:20px 0 16px;height:1px;\"></div></td></tr><tr><td colspan=\"2\" style=\"display:flex;\"><a href=\"https://twitter.com/DevtronL\" style=\"cursor:pointer;text-decoration:none;padding-right:12px;display:flex;\" target=\"_blank\"><img src=\"https://cdn.devtron.ai/images/twitter_social_dark.png\" style=\"width:20px;\"></a><a href=\"https://www.linkedin.com/company/devtron-labs/mycompany/\" style=\"cursor:pointer;text-decoration:none;padding-right:12px;display:flex;\" target=\"_blank\"><img src=\"https://cdn.devtron.ai/images/linkedin_social_dark.png\" style=\"width:20px;\"></a><a href=\"https://devtron.ai/blog/\" style=\"color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline;padding-right:12px;\" target=\"_blank\">Blog</a><a href=\"https://devtron.ai/\" style=\"color:#000a14;font-size:13px;line-height:20px;cursor:pointer;text-decoration:underline;\" target=\"_blank\">Website</a></td><td style=\"text-align:right;\"><div style=\"color:#767d84;font-size:13px;line-height:20px;\">© Devtron Labs 2024</div></td></tr></table>"}'
WHERE node_type = 'CI'
AND event_type_id = 3
AND channel_type='ses';

