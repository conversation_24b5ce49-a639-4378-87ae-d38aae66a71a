INSERT INTO public.event (id, event_type, description) VALUES (4, 'APPROVAL', '');
INSERT INTO "public"."notification_templates" (channel_type, node_type, event_type_id, template_name, template_payload)
VALUES ('smtp', 'CD', 4, 'CD approval smtp template', '{"from": "{{fromEmail}}",
"to": "{{toEmail}}","subject": "🛎️ Image approval requested | Application > {{appName}} | Environment > {{envName}}","html": "<table style=\"width: 600px; height: 485px;  border-collapse: collapse; padding: 20px;\"><tr style=\"background-color:#E5F2FF;\"><td colspan=\"2\" style=\"padding-left:16px;\"><h2 style=\"color:#000A14;\">Image approval request</h2><span>{{eventTime}}</span><br><span>by <strong style=\"color:#0066CC;\">{{triggeredBy}}</strong></span><br><br>{{#imageApprovalLink}}<a href=\"{{&imageApprovalLink}}\" style=\" height: 32px; padding: 7px 12px; line-height: 32px; font-size: 12px; font-weight: 600; border-radius: 4px; text-decoration: none; outline: none; min-width: 64px; text-transform: capitalize; text-align: center; background: #0066CC; color: #fff; border: 1px solid transparent; cursor: pointer;\">View request</a><br><br>{{/imageApprovalLink}}</td><td style=\"text-align: right;\"><img src=\"https://cdn.devtron.ai/images/img_build_notification.png\" style=\"height: 72px; width: 72px;\"></td></tr><tr><td colspan=\"3\"><hr><br><span>Application: <strong>{{appName}}</strong></span>&nbsp;&nbsp;|&nbsp;&nbsp;<span>Environment: <strong>{{envName}}</strong></span><br></span><br><br><hr><h3>Image Details</h3><span>Image tag <br><strong>{{imageTag}}</strong></span></td></tr><tr><td colspan=\"3\"><br><br><span style=\"display: {{commentDisplayStyle}}\">Comment<br><strong>{{comment}}</strong></span></td></tr><tr><td colspan=\"3\"><br><br><span style=\"display: {{tagDisplayStyle}}\">Tags<br><strong>{{tags}}</strong></span><br></td></tr></table>"}');
INSERT INTO "public"."notification_templates" (channel_type, node_type, event_type_id, template_name, template_payload)
VALUES ('ses', 'CD', 4, 'CD approval ses template', '{"from": "{{fromEmail}}",
"to": "{{toEmail}}","subject": "🛎️ Image approval requested | Application > {{appName}} | Environment > {{envName}}","html": "<table style=\"width: 600px; height: 485px;  border-collapse: collapse; padding: 20px;\"><tr style=\"background-color:#E5F2FF;\"><td colspan=\"2\" style=\"padding-left:16px;\"><h2 style=\"color:#000A14;\">Image approval request</h2><span>{{eventTime}}</span><br><span>by <strong style=\"color:#0066CC;\">{{triggeredBy}}</strong></span><br><br>{{#imageApprovalLink}}<a href=\"{{&imageApprovalLink}}\" style=\" height: 32px; padding: 7px 12px; line-height: 32px; font-size: 12px; font-weight: 600; border-radius: 4px; text-decoration: none; outline: none; min-width: 64px; text-transform: capitalize; text-align: center; background: #0066CC; color: #fff; border: 1px solid transparent; cursor: pointer;\">View request</a><br><br>{{/imageApprovalLink}}</td><td style=\"text-align: right;\"><img src=\"https://cdn.devtron.ai/images/img_build_notification.png\" style=\"height: 72px; width: 72px;\"></td></tr><tr><td colspan=\"3\"><hr><br><span>Application: <strong>{{appName}}</strong></span>&nbsp;&nbsp;|&nbsp;&nbsp;<span>Environment: <strong>{{envName}}</strong></span><br></span><br><br><hr><h3>Image Details</h3><span>Image tag <br><strong>{{imageTag}}</strong></span></td></tr><tr><td colspan=\"3\"><br><br><span style=\"display: {{commentDisplayStyle}}\">Comment<br><strong>{{comment}}</strong></span></td></tr><tr><td colspan=\"3\"><br><br><span style=\"display: {{tagDisplayStyle}}\">Tags<br><strong>{{tags}}</strong></span><br></td></tr></table>"}');


