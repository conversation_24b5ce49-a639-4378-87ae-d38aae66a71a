{{- if $.Values.appMetrics -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}
  labels:
    app: {{ template ".Chart.Name .name" . }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" . }}
    release: monitoring
spec:
  jobLabel: {{ template ".Chart.Name .name" $ }}
  endpoints:
    - port: envoy-admin
      interval: 30s
      path: /stats/prometheus
  selector:
    matchLabels:
      app: {{ template ".Chart.Name .name" $ }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
  namespaceSelector:
    matchNames:
      - {{.Release.Namespace}}
  podTargetLabels:
    - appId
    - envId
    - rollouts-pod-template-hash
{{- end }}