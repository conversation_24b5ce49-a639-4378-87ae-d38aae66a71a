{{ $svcName := include ".servicename" . }}
{{ $svcPort := (index .Values.ContainerPort 0).servicePort }}
{{- if $.Values.ingress.enabled -}}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ template ".Chart.Name .fullname" . }}-ingress
  namespace: {{ $.Values.NameSpace }}
  labels:
    app: {{ template ".Chart.Name .name" . }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" . }}
    release: {{ .Release.Name }}
    {{- if .Values.ingress.labels }}
{{ toYaml .Values.ingress.labels | indent 4 }}
    {{- end }}
{{- if .Values.ingress.annotations }}
  annotations:
{{ toYaml .Values.ingress.annotations | indent 4 }}
{{- end }}
spec:
  rules:
  {{- if or .Values.ingress.host .Values.ingress.path }}
    - host: {{ .Values.ingress.host }}
      http:
        paths:
          - path: {{ .Values.ingress.path }}
            backend:
              serviceName: {{ $svcName }}
              servicePort: {{ $svcPort }}
  {{- end }}
  {{- if .Values.ingress.hosts }}
  {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            backend:
              serviceName: {{ $svcName }}
              servicePort: {{ $svcPort }}
        {{- end }}
  {{- end }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
{{ toYaml .Values.ingress.tls | indent 4 }}
  {{- end -}}
{{- end }}
{{- if $.Values.ingressInternal.enabled }}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ template ".Chart.Name .fullname" . }}-ingress-internal
  namespace: {{ $.Values.NameSpace }}
  labels:
    app: {{ template ".Chart.Name .name" . }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" . }}
    release: {{ .Release.Name }}
{{- if .Values.ingressInternal.annotations }}
  annotations:
{{ toYaml .Values.ingressInternal.annotations | indent 4 }}
{{- end }}
spec:
  rules:
  {{- if or .Values.ingressInternal.host .Values.ingressInternal.path }}
    - host: {{ .Values.ingressInternal.host }}
      http:
        paths:
          - path: {{ .Values.ingressInternal.path }}
            backend:
              serviceName: {{ $svcName }}
              servicePort: {{ $svcPort }}
  {{- end }}
  {{- if .Values.ingressInternal.hosts }}
  {{- range .Values.ingressInternal.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            backend:
              serviceName: {{ $svcName }}
              servicePort: {{ $svcPort }}
        {{- end }}
  {{- end }}
  {{- end }}
  {{- if .Values.ingressInternal.tls }}
  tls:
{{ toYaml .Values.ingressInternal.tls | indent 4 }}
  {{- end -}}
{{- end }}
