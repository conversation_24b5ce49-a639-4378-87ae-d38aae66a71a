  {{- $hasCMEnvExists := false -}}
  {{- $hasCMVolumeExists := false -}}
  {{- if .Values.ConfigMaps.enabled }}
  {{- range .Values.ConfigMaps.maps }}
  {{- if eq .type "volume"}}
  {{- $hasCMVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasCMEnvExists = true}}
  {{- end }}
  {{- end }}
  {{- end }}

  {{- $hasPVCExists := false -}}
  {{- if .Values.persistentVolumeClaim.name }}
  {{- $hasPVCExists = true }}
  {{- end }}

  {{- $hasSecretEnvExists := false -}}
  {{- $hasSecretVolumeExists := false -}}
  {{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{- if eq .type "volume"}}
  {{- $hasSecretVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasSecretEnvExists = true}}
  {{- end }}
  {{- end }}
  {{- end }}
  {{ $CustomLabelsApp:= include "customPodLabelsContainsApp" . }}
  {{ $CustomLabelsRelease:= include "customPodLabelsContainsRelease" . }}


apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include ".Chart.Name .fullname" $ }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ .Values.pipelineName }}
{{- if .Values.deploymentLabels }}
{{ toYaml .Values.deploymentLabels | indent 4 }}
{{- end }}
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
{{- end }}

{{- if .Values.deploymentAnnotations }}
  annotations:
{{ toYaml .Values.deploymentAnnotations | indent 4 }}
{{- end }}
spec:
  selector:
    matchLabels:
{{- if .Values.customMatchLabels  }}
{{ toYaml .Values.customMatchLabels | indent 6 }}  
{{- else }}
      app: {{ .Values.customPodLabels.app | default  (include ".Chart.Name .name" $) }}
      release: {{ .Values.customPodLabels.release | default  $.Release.Name }}
{{- end }}
  replicas: {{ $.Values.replicaCount }}
  minReadySeconds: {{ $.Values.MinReadySeconds }}
  template:
    metadata:
    {{- if .Values.podAnnotations }}
      annotations:
      {{- range $key, $value := .Values.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    {{- end }}
      labels:
        {{- if not (eq "true" $CustomLabelsApp) }}
        app: {{ .Values.customPodLabels.app | default  (include ".Chart.Name .name" $) }}
        {{- end }}
        {{- if not (eq "true" $CustomLabelsRelease) }}
        release: {{ .Values.customPodLabels.release |default  $.Release.Name }}
        {{- end }}
        appId: {{ $.Values.app | quote }}
        envId: {{ $.Values.env | quote }}
{{- if .Values.customPodLabels  }}
{{ toYaml .Values.customPodLabels | indent 8 }}  
{{- end }}                
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 8 }}
{{- end }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
    spec:
{{- if $.Values.podExtraSpecs }}	
{{ toYaml .Values.podExtraSpecs | indent 6 }}	
{{- end }}
      terminationGracePeriodSeconds: {{ $.Values.GracePeriod }}
      restartPolicy: Always
{{- if $.Values.hostAliases }}
      hostAliases:
{{ toYaml .Values.hostAliases | indent 8 }}
{{- end }}
{{- if and $.Values.Spec.Affinity.Key $.Values.Spec.Affinity.Values }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ $.Values.Spec.Affinity.Key  }}
                operator: In
                values:
                - {{ $.Values.Spec.Affinity.Values | default "nodes"  }}
{{- else if $.Values.affinity.enabled }}
      affinity: 
{{ toYaml .Values.affinity.values | indent 8 }}
{{- end }}
{{- if $.Values.serviceAccountName }}
      serviceAccountName: {{ $.Values.serviceAccountName }}
{{- else  }}
      serviceAccountName: {{ template "serviceAccountName" . }}
{{- end }}
{{- if $.Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
{{- end }}      
  {{- if .Values.tolerations }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
  {{- end }}
{{- if $.Values.imagePullSecrets}}
      imagePullSecrets:
  {{- range .Values.imagePullSecrets }}
        - name: {{ . }}
  {{- end }}
{{- end}}
{{- if $.Values.topologySpreadConstraints }}
      topologySpreadConstraints:
{{- range $.Values.topologySpreadConstraints }}
      - maxSkew: {{ .maxSkew }}
        topologyKey: {{ .topologyKey }}
        whenUnsatisfiable: {{ .whenUnsatisfiable }}
        {{- if semverCompare "<=1.30-0" $.Capabilities.KubeVersion.GitVersion }}
        {{- if .minDomains }}
        minDomains: {{ .minDomains }}
        {{- end }}
        {{- end }}        
        {{- if .nodeAffinityPolicy }}
        nodeAffinityPolicy: {{ .nodeAffinityPolicy }}
        {{- end }}
        {{- if .nodeTaintsPolicy }}
        nodeTaintsPolicy: {{ .nodeTaintsPolicy }}
        {{- end }}                 
        labelSelector:
          matchLabels:
          {{- if and .autoLabelSelector .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- else if .autoLabelSelector }}
            app: {{ template ".Chart.Name .name" $ }}
            appId: {{ $.Values.app | quote }}
            envId: {{ $.Values.env | quote }}
            release: {{ $.Release.Name }}
          {{- else if .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- end }}
{{- end }}
{{- end }}
{{- if $.Values.topologySpreadConstraint }}
      topologySpreadConstraints: 
{{ toYaml .Values.topologySpreadConstraint }}  
{{- end }}    
{{- if $.Values.podSecurityContext }}
      securityContext:
{{ toYaml .Values.podSecurityContext | indent 8 }}
{{- end }}
{{- if $.Values.restartPolicy }}
      restartPolicy: {{  $.Values.restartPolicy }}
{{- end }}
{{- if $.Values.initContainers}}
      initContainers:
{{- range $i, $c := .Values.initContainers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-init-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .args}}
          args:
{{ toYaml .args | indent 12 -}}
{{- end }}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end }}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}
      containers:
        - name: {{ $.Chart.Name }}
          image: "{{ .Values.server.deployment.image }}:{{ .Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          {{- if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
           {{- if $.Values.containerSpec.lifecycle.postStart }}
           postStart:
{{ toYaml $.Values.containerSpec.lifecycle.postStart | indent 12 -}}
           {{- end }}
          {{- end }}
{{- if and $.Values.containerSecurityContext $.Values.privileged }}
          securityContext:
            privileged: true
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- else if $.Values.privileged }}
          securityContext:
            privileged: true
{{- else if $.Values.containerSecurityContext }}
          securityContext:
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- end }}
{{- if $.Values.containerExtraSpecs }}	
{{ toYaml .Values.containerExtraSpecs | indent 10 }}	
{{- end }}
{{- if $.Values.resizePolicy }}
          resizePolicy:
{{ toYaml .Values.resizePolicy | indent 12 }}          
{{- end }}          
          ports:
          {{- range $.Values.ContainerPort }}
            - name: {{ .name}}
              containerPort: {{ .port  }}
              protocol: {{ .protocol | default "TCP" }}
          {{- end}}
{{- if and $.Values.command.enabled $.Values.command.workingDir }}
          workingDir: {{ $.Values.command.workingDir }}
{{- end}}
{{- if and $.Values.command.value $.Values.command.enabled}}
          command:
{{ toYaml $.Values.command.value | indent 12 -}}
{{- end}}
{{- if and $.Values.args.value $.Values.args.enabled}}
          args:
{{ toYaml $.Values.args.value | indent 12 -}}
{{- end }}
          env:
            - name: CONFIG_HASH
              value: {{ include (print $.Chart.Name "/templates/configmap.yaml") . | sha256sum }}{{ if and (.Values.devtronInternal) (.Values.devtronInternal.containerSpecs.ConfigHash) }}{{ .Values.devtronInternal.containerSpecs.ConfigHash }}{{ end }}
            - name: SECRET_HASH
              value: {{ include (print $.Chart.Name "/templates/secret.yaml") . | sha256sum }}{{ if and (.Values.devtronInternal) (.Values.devtronInternal.containerSpecs.SecretHash) }}{{ .Values.devtronInternal.containerSpecs.SecretHash }}{{ end }}
            - name: DEVTRON_APP_NAME
              value: {{ template ".Chart.Name .name" $ }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: DEVTRON_CONTAINER_REPO
              value: "{{ .Values.server.deployment.image }}"
            - name: DEVTRON_CONTAINER_TAG
              value: "{{ .Values.server.deployment.image_tag }}"
          {{- range $.Values.EnvVariablesFromFieldPath }}
            - name: {{ .name }}
              valueFrom:
                fieldRef:
                 fieldPath: {{ .fieldPath }}
          {{- end}}
          {{- range $.Values.EnvVariables }}
          {{- if and .name .value }}
            - name: {{ .name }}
              value: {{ .value | quote }}
          {{- end }}
          {{- end }}
          {{- range $.Values.EnvVariablesFromSecretKeys }}
          {{- if and .name .secretName .keyName }}
            - name: {{ .name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .secretName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- range $.Values.EnvVariablesFromConfigMapKeys }}
          {{- if and .name .configMapName .keyName }}
            - name: {{ .name }}
              valueFrom:
                configMapKeyRef:
                  name: {{ .configMapName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- if or (and ($hasCMEnvExists) (.Values.ConfigMaps.enabled)) (and ($hasSecretEnvExists) (.Values.ConfigSecrets.enabled)) }}
          envFrom:
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "environment" }}
          - configMapRef:
              {{- if eq .external true }}
              name: {{ .name }}
              {{- else if eq .external false }}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "environment" }}
          - secretRef:
              {{if eq .external true}}
              name: {{ .name }}
              {{else if eq .external false}}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

{{- if or $.Values.LivenessProbe.Path $.Values.LivenessProbe.command $.Values.LivenessProbe.tcp $.Values.LivenessProbe.grpc }}
          livenessProbe:
{{- if $.Values.LivenessProbe.Path }}
            httpGet:
              path: {{ $.Values.LivenessProbe.Path  }}
              port: {{ $.Values.LivenessProbe.port }}
              scheme: {{ $.Values.LivenessProbe.scheme }}
            {{- if $.Values.LivenessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.LivenessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.LivenessProbe.command }}
            exec:
              command:
{{ toYaml .Values.LivenessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.LivenessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.LivenessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.LivenessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.LivenessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.LivenessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.LivenessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.LivenessProbe.failureThreshold  }}
          {{- if $.Values.LivenessProbe.grpc }}
            grpc:
{{ toYaml .Values.LivenessProbe.grpc | indent 14 }}
          {{- end }}
{{- end }}
{{- if or $.Values.ReadinessProbe.Path  $.Values.ReadinessProbe.command $.Values.ReadinessProbe.tcp $.Values.ReadinessProbe.grpc }}
          readinessProbe:
{{- if $.Values.ReadinessProbe.Path }}
            httpGet:
              path: {{ $.Values.ReadinessProbe.Path  }}
              port: {{ $.Values.ReadinessProbe.port }}
              scheme: {{ $.Values.ReadinessProbe.scheme }}
            {{- if $.Values.ReadinessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.ReadinessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.ReadinessProbe.command }}
            exec:
              command:
{{ toYaml .Values.ReadinessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.ReadinessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.ReadinessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.ReadinessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.ReadinessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.ReadinessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.ReadinessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.ReadinessProbe.failureThreshold  }}
          {{- if $.Values.ReadinessProbe.grpc }}
            grpc:
{{ toYaml .Values.ReadinessProbe.grpc | indent 14 }}
            {{- end}}
{{- end }}
{{- $resources := $.Values.resources | default dict }}
{{- $gpu := $.Values.gpu | default dict }}
{{- $gpuEnabled := and (hasKey $gpu "enabled") $gpu.enabled (hasKey $gpu "provider") (hasKey $gpu "resources") }}
{{- $gpuLimits := dict }}
{{- $gpuRequests := dict }}
{{- if $gpuEnabled }}
  {{- $gpuLimits = dict $gpu.provider ($gpu.resources.limits | default 0) }}
  {{- $gpuLimits = dict "limits" $gpuLimits }}
  {{- $gpuRequests = dict $gpu.provider ($gpu.resources.requests | default 0) }}
  {{- $gpuRequests = dict "requests" $gpuRequests }}
{{- end }}
{{- $limits := merge (get $resources "limits" | default dict) (get $gpuLimits "limits" | default dict) }}
{{- $requests := merge (get $resources "requests" | default dict) (get $gpuRequests "requests" | default dict) }}
{{- if or $limits $requests }}
          resources:
{{- if $limits }}
            limits:
{{ toYaml $limits | indent 14 }}
{{- end }}
{{- if $requests }}
            requests:
{{ toYaml $requests | indent 14 }}
{{- end }}
{{- end }}
{{- if or $.Values.StartupProbe.Path  $.Values.StartupProbe.command $.Values.StartupProbe.tcp $.Values.StartupProbe.grpc }}
          startupProbe:
{{- if $.Values.StartupProbe.Path }}
            httpGet:
              path: {{ $.Values.StartupProbe.Path  }}
              port: {{ $.Values.StartupProbe.port }}
            {{- if $.Values.StartupProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.StartupProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
        {{- end }}
{{- end }}
{{- if $.Values.StartupProbe.command }}
            exec:
              command:
{{ toYaml .Values.StartupProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.StartupProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.StartupProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.StartupProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.StartupProbe.periodSeconds  }}
            successThreshold: {{ $.Values.StartupProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.StartupProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.StartupProbe.failureThreshold  }}
            {{- if $.Values.StartupProbe.grpc }}
            grpc:
{{ toYaml .Values.StartupProbe.grpc | indent 14 }}
            {{- end}}
{{- end }}
          volumeMounts:
{{- with .Values.volumeMounts }}
{{ toYaml . | trim | indent 12 }}
{{- end }}
{{- if  $.Values.persistentVolumeClaim.name }}
            - name: {{ .Values.persistentVolumeClaim.name }}-vol
              mountPath: {{ .Values.persistentVolumeClaim.mountPath | default "/tmp" }}
{{- end}}
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}
	  
          {{- else }}
          {{- range $k, $v := .data }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}
	      
          {{- else }}
          {{if (or (eq .externalType "ESO_GoogleSecretsManager") (eq .externalType "ESO_AWSSecretsManager") (eq .externalType "ESO_HashiCorpVault") (eq .externalType "ESO_AzureSecretsManager"))}}
          {{- if and (.esoSubPath) (ne (len .esoSubPath) 0) }}
          {{- range .esoSubPath }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ . }}
              subPath: {{ . }}  
          {{- end }}
          {{- else }}
          {{- range .esoSecretData.esoData }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ .secretKey }}
              subPath: {{ .secretKey }}  
          {{- end }}
          {{- end }}
          {{- else }}            
          {{- range $k, $v := .data }} # for others secrets the mount path will be .data[i].secretKey
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}          
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) }} []{{- end }}
          {{- if and (eq (len .Values.volumeMounts) 0) (eq ($hasPVCExists) false) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) }} [] {{- end }}
{{- if $.Values.appMetrics }}
        - name: envoy
          image: {{ $.Values.envoyproxy.image | default "quay.io/devtron/envoy:v1.16.0"}}
          {{- if $.Values.envoyproxy.lifecycle }}
          lifecycle:
{{ toYaml .Values.envoyproxy.lifecycle | indent 12 -}}
          {{- else if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
          {{- end }}
          resources:
{{ toYaml $.Values.envoyproxy.resources | trim | indent 12 }}
          ports:
            - containerPort: 9901
              protocol: TCP
              name: envoy-admin
              {{- range $index, $element := .Values.ContainerPort }}
            - name: envoy-{{ $element.name}}
              containerPort: {{ $element.envoyPort | default (add 8790 $index) }}
              protocol: TCP
              {{- end }}
          command: ["/usr/local/bin/envoy"]
          args: ["-c", "/etc/envoy-config/envoy-config.json", "-l", "info", "--log-format", "[METADATA][%Y-%m-%d %T.%e][%t][%l][%n] %v"]
          volumeMounts:
            - name: {{ $.Values.envoyproxy.configMapName | default "envoy-config-volume" }}
              mountPath: /etc/envoy-config/
{{- if $.Values.envoyproxy.readinessProbe}}
          readinessProbe:
{{ toYaml $.Values.envoyproxy.readinessProbe | indent 12}} 
{{- end }}  
{{- if $.Values.envoyproxy.livenessProbe}}
          livenessProbe:
{{ toYaml $.Values.envoyproxy.livenessProbe | indent 12}} 
{{- end }}                     
{{- end}}
{{- if $.Values.containers }}
{{- range $i, $c := .Values.containers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-sidecontainer-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .env }}
          env:
{{ toYaml .env | indent 12 }}
{{- end }}
 {{- if .envFrom }}
          envFrom:
{{ toYaml .env | indent 12 }}
{{- end }}                   
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .resizePolicy }}
          resizePolicy:
{{ toYaml .resziePolicy | indent 12}}
{{- end }}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end}}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}


      volumes:
  {{- if $.Values.appMetrics }}
        - name: envoy-config-volume
          configMap:
            name: sidecar-config-{{ template ".Chart.Name .name" $ }}
  {{- end }}
{{- with .Values.volumes }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
{{- if .Values.persistentVolumeClaim.name }}
        - name: {{.Values.persistentVolumeClaim.name}}-vol
          persistentVolumeClaim:
            claimName: {{.Values.persistentVolumeClaim.name }}
{{- end}}
      {{- if .Values.ConfigMaps.enabled }}
      {{- range .Values.ConfigMaps.maps }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          configMap:
            {{- if eq .external true }}
            name: {{ .name }}
            {{- else if eq .external false }}
            name: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}

      {{- if .Values.ConfigSecrets.enabled }}
      {{- range .Values.ConfigSecrets.secrets }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          secret:
            {{- if eq .external true }}
            secretName: {{ .name }}
            {{- else if eq .external false }}
            secretName: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}
      {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) (eq (.Values.appMetrics) false) }} []{{- end }}
      {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) (eq (.Values.appMetrics) false) }} [] {{- end }}

  revisionHistoryLimit: 3
##  pauseForSecondsBeforeSwitchActive: {{ $.Values.pauseForSecondsBeforeSwitchActive }}
#  waitForSecondsBeforeScalingDown: {{ $.Values.waitForSecondsBeforeScalingDown }}
  strategy:
    {{- if eq .Values.deploymentType "ROLLING" }}
    type: "RollingUpdate"
    rollingUpdate:
      maxSurge: {{ $.Values.deployment.strategy.rolling.maxSurge }}
      maxUnavailable: {{ $.Values.deployment.strategy.rolling.maxUnavailable }}
    {{- end }}
    {{- if eq .Values.deploymentType "RECREATE" }}
    type: "Recreate"
    {{- end }}
{{- if $.Values.secondaryWorkload.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include ".Chart.Name .fullname" $ }}-{{ $.Values.secondaryWorkload.postfix | default "sec" }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ .Values.pipelineName }}
{{- if .Values.deploymentLabels }}
{{ toYaml .Values.deploymentLabels | indent 4 }}
{{- end }}
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
{{- end }}

{{- if .Values.deploymentAnnotations }}
  annotations:
{{ toYaml .Values.deploymentAnnotations | indent 4 }}
{{- end }}
spec:
  selector:
    matchLabels:
{{- if .Values.customMatchLabels  }}
{{ toYaml .Values.customMatchLabels | indent 6 }}  
{{- else }}
      app: {{ .Values.customPodLabels.app | default  (include ".Chart.Name .name" $) }}
      release: {{ .Values.customPodLabels.release | default  $.Release.Name }}
{{- end }}
  replicas: {{ $.Values.secondaryWorkload.replicaCount | default 1 }}
  minReadySeconds: {{ $.Values.MinReadySeconds }}
  template:
    metadata:
    {{- if .Values.podAnnotations }}
      annotations:
      {{- range $key, $value := .Values.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    {{- end }}
      labels:
        {{- if not (eq "true" $CustomLabelsApp) }}
        app: {{ .Values.customPodLabels.app | default  (include ".Chart.Name .name" $) }}
        {{- end }}
        {{- if not (eq "true" $CustomLabelsRelease) }}
        release: {{ .Values.customPodLabels.release |default  $.Release.Name }}
        {{- end }}
        appId: {{ $.Values.app | quote }}
        envId: {{ $.Values.env | quote }}
{{- if .Values.customPodLabels  }}
{{ toYaml .Values.customPodLabels | indent 8 }}  
{{- end }}                
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 8 }}
{{- end }}
{{- if .Values.podLabels }}
{{ toYaml .Values.podLabels | indent 8 }}
{{- end }}
    spec:
{{- if $.Values.podExtraSpecs }}	
{{ toYaml .Values.podExtraSpecs | indent 6 }}	
{{- end }}
      terminationGracePeriodSeconds: {{ $.Values.GracePeriod }}
      restartPolicy: Always
{{- if $.Values.hostAliases }}
      hostAliases:
{{ toYaml .Values.hostAliases | indent 8 }}
{{- end }}
{{- with $.Values.secondaryWorkload }}
{{- if and .Spec.Affinity.Key .Spec.Affinity.Values }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Spec.Affinity.Key  }}
                operator: In
                values:
                - {{ .Spec.Affinity.Values | default "nodes"  }}
{{- else if .affinity.enabled }}
      affinity: 
{{ toYaml .affinity.values | indent 8 }}
{{- end }}
{{- end }}
{{- if $.Values.serviceAccountName }}
      serviceAccountName: {{ $.Values.serviceAccountName }}
{{- else  }}
      serviceAccountName: {{ template "serviceAccountName" . }}
{{- end }}
{{- if $.Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
{{- end }}      
  {{- if $.Values.secondaryWorkload.tolerations }}
      tolerations:
{{ toYaml $.Values.secondaryWorkload.tolerations | indent 8 }}
  {{- end }}
{{- if $.Values.imagePullSecrets}}
      imagePullSecrets:
  {{- range .Values.imagePullSecrets }}
        - name: {{ . }}
  {{- end }}
{{- end}}
{{- if $.Values.topologySpreadConstraints }}
      topologySpreadConstraints:
{{- range $.Values.topologySpreadConstraints }}
      - maxSkew: {{ .maxSkew }}
        topologyKey: {{ .topologyKey }}
        whenUnsatisfiable: {{ .whenUnsatisfiable }}
        {{- if semverCompare "<=1.30-0" $.Capabilities.KubeVersion.GitVersion }}
        {{- if .minDomains }}
        minDomains: {{ .minDomains }}
        {{- end }}
        {{- end }}        
        {{- if .nodeAffinityPolicy }}
        nodeAffinityPolicy: {{ .nodeAffinityPolicy }}
        {{- end }}
        {{- if .nodeTaintsPolicy }}
        nodeTaintsPolicy: {{ .nodeTaintsPolicy }}
        {{- end }}                 
        labelSelector:
          matchLabels:
          {{- if and .autoLabelSelector .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- else if .autoLabelSelector }}
            app: {{ template ".Chart.Name .name" $ }}
            appId: {{ $.Values.app | quote }}
            envId: {{ $.Values.env | quote }}
            release: {{ $.Release.Name }}
          {{- else if .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- end }}
{{- end }}
{{- end }}
{{- if $.Values.topologySpreadConstraint }}
      topologySpreadConstraints: 
{{ toYaml .Values.topologySpreadConstraint }}  
{{- end }}    
{{- if $.Values.podSecurityContext }}
      securityContext:
{{ toYaml .Values.podSecurityContext | indent 8 }}
{{- end }}
{{- if $.Values.restartPolicy }}
      restartPolicy: {{  $.Values.restartPolicy }}
{{- end }}
{{- if $.Values.initContainers}}
      initContainers:
{{- range $i, $c := .Values.initContainers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-init-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .args}}
          args:
{{ toYaml .args | indent 12 -}}
{{- end}}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end}}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}
      containers:
        - name: {{ $.Chart.Name }}
          image: "{{ .Values.server.deployment.image }}:{{ .Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          {{- if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
           {{- if $.Values.containerSpec.lifecycle.postStart }}
           postStart:
{{ toYaml $.Values.containerSpec.lifecycle.postStart | indent 12 -}}
           {{- end }}
          {{- end }}
{{- if and $.Values.containerSecurityContext $.Values.privileged }}
          securityContext:
            privileged: true
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- else if $.Values.privileged }}
          securityContext:
            privileged: true
{{- else if $.Values.containerSecurityContext }}
          securityContext:
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- end }}
{{- if $.Values.containerExtraSpecs }}	
{{ toYaml .Values.containerExtraSpecs | indent 10 }}	
{{- end }}
{{- if $.Values.resizePolicy }}
          resizePolicy:
{{ toYaml .Values.resizePolicy | indent 12 }}          
{{- end }}          
          ports:
          {{- range $.Values.ContainerPort }}
            - name: {{ .name}}
              containerPort: {{ .port  }}
              protocol: {{ .protocol | default "TCP" }}
          {{- end}}
{{- if and $.Values.command.enabled $.Values.command.workingDir }}
          workingDir: {{ $.Values.command.workingDir }}
{{- end}}
{{- if and $.Values.command.value $.Values.command.enabled}}
          command:
{{ toYaml $.Values.command.value | indent 12 -}}
{{- end}}
{{- if and $.Values.args.value $.Values.args.enabled}}
          args:
{{ toYaml $.Values.args.value | indent 12 -}}
{{- end }}
          env:
            - name: CONFIG_HASH
              value: {{ include (print $.Chart.Name "/templates/configmap.yaml") . | sha256sum }}{{ if and (.Values.devtronInternal) (.Values.devtronInternal.containerSpecs.ConfigHash) }}{{ .Values.devtronInternal.containerSpecs.ConfigHash }}{{ end }}
            - name: SECRET_HASH
              value: {{ include (print $.Chart.Name "/templates/secret.yaml") . | sha256sum }}{{ if and (.Values.devtronInternal) (.Values.devtronInternal.containerSpecs.SecretHash) }}{{ .Values.devtronInternal.containerSpecs.SecretHash }}{{ end }}
            - name: DEVTRON_APP_NAME
              value: {{ template ".Chart.Name .name" $ }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: DEVTRON_CONTAINER_REPO
              value: "{{ .Values.server.deployment.image }}"
            - name: DEVTRON_CONTAINER_TAG
              value: "{{ .Values.server.deployment.image_tag }}"
          {{- range $.Values.EnvVariablesFromFieldPath }}
            - name: {{ .name }}
              valueFrom:
                fieldRef:
                 fieldPath: {{ .fieldPath }}
          {{- end}}
          {{- range $.Values.EnvVariables }}
          {{- if and .name .value }}
            - name: {{ .name }}
              value: {{ .value | quote }}
          {{- end }}
          {{- end }}
          {{- range $.Values.EnvVariablesFromSecretKeys }}
          {{- if and .name .secretName .keyName }}
            - name: {{ .name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .secretName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- range $.Values.EnvVariablesFromConfigMapKeys }}
          {{- if and .name .configMapName .keyName }}
            - name: {{ .name }}
              valueFrom:
                configMapKeyRef:
                  name: {{ .configMapName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- if or (and ($hasCMEnvExists) (.Values.ConfigMaps.enabled)) (and ($hasSecretEnvExists) (.Values.ConfigSecrets.enabled)) }}
          envFrom:
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "environment" }}
          - configMapRef:
              {{- if eq .external true }}
              name: {{ .name }}
              {{- else if eq .external false }}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "environment" }}
          - secretRef:
              {{if eq .external true}}
              name: {{ .name }}
              {{else if eq .external false}}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

{{- if or $.Values.LivenessProbe.Path $.Values.LivenessProbe.command $.Values.LivenessProbe.tcp $.Values.LivenessProbe.grpc }}
          livenessProbe:
{{- if $.Values.LivenessProbe.Path }}
            httpGet:
              path: {{ $.Values.LivenessProbe.Path  }}
              port: {{ $.Values.LivenessProbe.port }}
              scheme: {{ $.Values.LivenessProbe.scheme }}
            {{- if $.Values.LivenessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.LivenessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.LivenessProbe.command }}
            exec:
              command:
{{ toYaml .Values.LivenessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.LivenessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.LivenessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.LivenessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.LivenessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.LivenessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.LivenessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.LivenessProbe.failureThreshold  }}
          {{- if $.Values.LivenessProbe.grpc }}
            grpc:
{{ toYaml .Values.LivenessProbe.grpc | indent 14 }}
          {{- end }}
{{- end }}
{{- if or $.Values.ReadinessProbe.Path  $.Values.ReadinessProbe.command $.Values.ReadinessProbe.tcp $.Values.ReadinessProbe.grpc }}
          readinessProbe:
{{- if $.Values.ReadinessProbe.Path }}
            httpGet:
              path: {{ $.Values.ReadinessProbe.Path  }}
              port: {{ $.Values.ReadinessProbe.port }}
              scheme: {{ $.Values.ReadinessProbe.scheme }}
            {{- if $.Values.ReadinessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.ReadinessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.ReadinessProbe.command }}
            exec:
              command:
{{ toYaml .Values.ReadinessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.ReadinessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.ReadinessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.ReadinessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.ReadinessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.ReadinessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.ReadinessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.ReadinessProbe.failureThreshold  }}
          {{- if $.Values.ReadinessProbe.grpc }}
            grpc:
{{ toYaml .Values.ReadinessProbe.grpc | indent 14 }}
            {{- end}}
{{- end }}
          resources:
{{ toYaml $.Values.resources | trim | indent 12 }}
{{- if or $.Values.StartupProbe.Path  $.Values.StartupProbe.command $.Values.StartupProbe.tcp $.Values.StartupProbe.grpc }}
          startupProbe:
{{- if $.Values.StartupProbe.Path }}
            httpGet:
              path: {{ $.Values.StartupProbe.Path  }}
              port: {{ $.Values.StartupProbe.port }}
            {{- if $.Values.StartupProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.StartupProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
        {{- end }}
{{- end }}
{{- if $.Values.StartupProbe.command }}
            exec:
              command:
{{ toYaml .Values.StartupProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.StartupProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.StartupProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.StartupProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.StartupProbe.periodSeconds  }}
            successThreshold: {{ $.Values.StartupProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.StartupProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.StartupProbe.failureThreshold  }}
            {{- if $.Values.StartupProbe.grpc }}
            grpc:
{{ toYaml .Values.StartupProbe.grpc | indent 14 }}
            {{- end}}
{{- end }}
          volumeMounts:
{{- with .Values.volumeMounts }}
{{ toYaml . | trim | indent 12 }}
{{- end }}
{{- if  $.Values.persistentVolumeClaim.name }}
            - name: {{ .Values.persistentVolumeClaim.name }}-vol
              mountPath: {{ .Values.persistentVolumeClaim.mountPath | default "/tmp" }}
{{- end}}
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}
	  
          {{- else }}
          {{- range $k, $v := .data }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}
	      
          {{- else }}
          {{if (or (eq .externalType "ESO_GoogleSecretsManager") (eq .externalType "ESO_AWSSecretsManager") (eq .externalType "ESO_HashiCorpVault") (eq .externalType "ESO_AzureSecretsManager"))}}
          {{- if and (.esoSubPath) (ne (len .esoSubPath) 0) }}
          {{- range .esoSubPath }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ . }}
              subPath: {{ . }}  
          {{- end }}
          {{- else }}
          {{- range .esoSecretData.esoData }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ .secretKey }}
              subPath: {{ .secretKey }}  
          {{- end }}
          {{- end }}
          {{- else }}            
          {{- range $k, $v := .data }} # for others secrets the mount path will be .data[i].secretKey
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}          
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) }} []{{- end }}
          {{- if and (eq (len .Values.volumeMounts) 0) (eq ($hasPVCExists) false) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) }} [] {{- end }}
{{- if $.Values.appMetrics }}
        - name: envoy
          image: {{ $.Values.envoyproxy.image | default "quay.io/devtron/envoy:v1.16.0"}}
          {{- if $.Values.envoyproxy.lifecycle }}
          lifecycle:
{{ toYaml .Values.envoyproxy.lifecycle | indent 12 -}}
          {{- else if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
          {{- end }}
          resources:
{{ toYaml $.Values.envoyproxy.resources | trim | indent 12 }}
          ports:
            - containerPort: 9901
              protocol: TCP
              name: envoy-admin
              {{- range $index, $element := .Values.ContainerPort }}
            - name: envoy-{{ $element.name}}
              containerPort: {{ $element.envoyPort | default (add 8790 $index) }}
              protocol: TCP
              {{- end }}
          command: ["/usr/local/bin/envoy"]
          args: ["-c", "/etc/envoy-config/envoy-config.json", "-l", "info", "--log-format", "[METADATA][%Y-%m-%d %T.%e][%t][%l][%n] %v"]
          volumeMounts:
            - name: {{ $.Values.envoyproxy.configMapName | default "envoy-config-volume" }}
              mountPath: /etc/envoy-config/
{{- if $.Values.envoyproxy.readinessProbe}}
          readinessProbe:
{{ toYaml $.Values.envoyproxy.readinessProbe | indent 12}} 
{{- end }}  
{{- if $.Values.envoyproxy.livenessProbe}}
          livenessProbe:
{{ toYaml $.Values.envoyproxy.livenessProbe | indent 12}} 
{{- end }}                     
{{- end}}
{{- if $.Values.containers }}
{{- range $i, $c := .Values.containers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-sidecontainer-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .env }}
          env:
{{ toYaml .env | indent 12 }}
{{- end }}
 {{- if .envFrom }}
          envFrom:
{{ toYaml .env | indent 12 }}
{{- end }}                   
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .resizePolicy }}
          resizePolicy:
{{ toYaml .resziePolicy | indent 12}}
{{- end }}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end}}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}


      volumes:
  {{- if $.Values.appMetrics }}
        - name: envoy-config-volume
          configMap:
            name: sidecar-config-{{ template ".Chart.Name .name" $ }}
  {{- end }}
{{- with .Values.volumes }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
{{- if .Values.persistentVolumeClaim.name }}
        - name: {{.Values.persistentVolumeClaim.name}}-vol
          persistentVolumeClaim:
            claimName: {{.Values.persistentVolumeClaim.name }}
{{- end}}
      {{- if .Values.ConfigMaps.enabled }}
      {{- range .Values.ConfigMaps.maps }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          configMap:
            {{- if eq .external true }}
            name: {{ .name }}
            {{- else if eq .external false }}
            name: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}

      {{- if .Values.ConfigSecrets.enabled }}
      {{- range .Values.ConfigSecrets.secrets }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          secret:
            {{- if eq .external true }}
            secretName: {{ .name }}
            {{- else if eq .external false }}
            secretName: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}
      {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) (eq (.Values.appMetrics) false) }} []{{- end }}
      {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) (eq (.Values.appMetrics) false) }} [] {{- end }}

  revisionHistoryLimit: 3
##  pauseForSecondsBeforeSwitchActive: {{ $.Values.pauseForSecondsBeforeSwitchActive }}
#  waitForSecondsBeforeScalingDown: {{ $.Values.waitForSecondsBeforeScalingDown }}
  strategy:
    {{- if eq .Values.deploymentType "ROLLING" }}
    type: "RollingUpdate"
    rollingUpdate:
      maxSurge: {{ $.Values.deployment.strategy.rolling.maxSurge }}
      maxUnavailable: {{ $.Values.deployment.strategy.rolling.maxUnavailable }}
    {{- end }}
    {{- if eq .Values.deploymentType "RECREATE" }}
    type: "Recreate"
    {{- end }}
{{- end }}

