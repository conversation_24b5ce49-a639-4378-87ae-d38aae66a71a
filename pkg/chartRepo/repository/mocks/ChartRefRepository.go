// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	mock "github.com/stretchr/testify/mock"
)

// ChartRefRepository is an autogenerated mock type for the ChartRefRepository type
type ChartRefRepository struct {
	mock.Mock
}

// CheckIfDataExists provides a mock function with given fields: name, version
func (_m *ChartRefRepository) CheckIfDataExists(name string, version string) (bool, error) {
	ret := _m.Called(name, version)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = rf(name, version)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(name, version)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Fetch<PERSON>hart provides a mock function with given fields: name
func (_m *ChartRefRepository) FetchChart(name string) ([]*chartRepoRepository.ChartRef, error) {
	ret := _m.Called(name)

	var r0 []*chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func(string) []*chartRepoRepository.ChartRef); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchChartInfoByUploadFlag provides a mock function with given fields: userUploaded
func (_m *ChartRefRepository) FetchChartInfoByUploadFlag(userUploaded bool) ([]*chartRepoRepository.ChartRef, error) {
	ret := _m.Called(userUploaded)

	var r0 []*chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func(bool) []*chartRepoRepository.ChartRef); ok {
		r0 = rf(userUploaded)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(bool) error); ok {
		r1 = rf(userUploaded)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchInfoOfChartConfiguredInApp provides a mock function with given fields: appId
func (_m *ChartRefRepository) FetchInfoOfChartConfiguredInApp(appId int) (*chartRepoRepository.ChartRef, error) {
	ret := _m.Called(appId)

	var r0 *chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func(int) *chartRepoRepository.ChartRef); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *ChartRefRepository) FindById(id int) (*chartRepoRepository.ChartRef, error) {
	ret := _m.Called(id)

	var r0 *chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func(int) *chartRepoRepository.ChartRef); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByVersionAndName provides a mock function with given fields: name, version
func (_m *ChartRefRepository) FindByVersionAndName(name string, version string) (*chartRepoRepository.ChartRef, error) {
	ret := _m.Called(name, version)

	var r0 *chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func(string, string) *chartRepoRepository.ChartRef); ok {
		r0 = rf(name, version)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(name, version)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAll provides a mock function with given fields:
func (_m *ChartRefRepository) GetAll() ([]*chartRepoRepository.ChartRef, error) {
	ret := _m.Called()

	var r0 []*chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func() []*chartRepoRepository.ChartRef); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllChartMetadata provides a mock function with given fields:
func (_m *ChartRefRepository) GetAllChartMetadata() ([]*chartRepoRepository.ChartRefMetaData, error) {
	ret := _m.Called()

	var r0 []*chartRepoRepository.ChartRefMetaData
	if rf, ok := ret.Get(0).(func() []*chartRepoRepository.ChartRefMetaData); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartRepoRepository.ChartRefMetaData)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDefault provides a mock function with given fields:
func (_m *ChartRefRepository) GetDefault() (*chartRepoRepository.ChartRef, error) {
	ret := _m.Called()

	var r0 *chartRepoRepository.ChartRef
	if rf, ok := ret.Get(0).(func() *chartRepoRepository.ChartRef); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartRepoRepository.ChartRef)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: chartRepo
func (_m *ChartRefRepository) Save(chartRepo *chartRepoRepository.ChartRef) error {
	ret := _m.Called(chartRepo)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartRepoRepository.ChartRef) error); ok {
		r0 = rf(chartRepo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewChartRefRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewChartRefRepository creates a new instance of ChartRefRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewChartRefRepository(t mockConstructorTestingTNewChartRefRepository) *ChartRefRepository {
	mock := &ChartRefRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
