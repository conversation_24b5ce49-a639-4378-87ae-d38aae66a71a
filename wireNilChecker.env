PG_PORT=30468
HOSTNAME=e2ecc77bfb24
TEST_BRANCH=
HOME=/root
OLDPWD=/
PG_PASSWORD=devtronpg
RUNTIME_CONFIG_LOCAL_DEV=true
PG_USER=postgres
BLOB_STORAGE_S3_BUCKET_VERSIONED=true
DEVTRON_DEX_SECRET_NAMESPACE=devtroncd
AZURE_GATEWAY_CONNECTION_INSECURE=false
CLOUD_PROVIDER=MINIO
BLOB_STORAGE_S3_ENDPOINT_INSECURE=false
TERM=xterm
DEVTRON_INSTALLATION_TYPE=enterprise
DEVTRONCD_NAMESPACE=devtroncd
BLOB_STORAGE_ENABLED=true
CI_NODE_LABEL_SELECTOR=abc=dbc,bcd=def
CD_LIMIT_CI_CPU=0.5
PG_DATABASE=orchestrator
ORCH_HOST=http://devtroncd-orchestrator-service-prod.devtroncd/webhook/msg/nats
DOCKER_VERSION=26.0.0
DOCKER_TLS_CERTDIR=/certs
DEVTRON_DEFAULT_NAMESPACE=devtroncd
IN_APP_LOGGING_ENABLED=false
PWD=/test
SCOPED_VARIABLE_ENABLED=true
EXECUTE_WIRE_NIL_CHECKER=true
TEST_BRANCH=
LATEST_HASH=
GOPATH=/usr/local/go
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/go/bin
PROXY_SERVICE_CONFIG={}


