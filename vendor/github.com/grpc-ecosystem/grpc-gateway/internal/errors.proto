syntax = "proto3";
package grpc.gateway.runtime;
option go_package = "internal";

import "google/protobuf/any.proto";

// Error is the generic error returned from unary RPCs.
message Error {
	string error = 1;
	// This is to make the error more compatible with users that expect errors to be Status objects:
	// https://github.com/grpc/grpc/blob/master/src/proto/grpc/status/status.proto
	// It should be the exact same message as the Error field.
	int32 code = 2;
	string message = 3;
	repeated google.protobuf.Any details = 4;
}

// StreamError is a response type which is returned when
// streaming rpc returns an error.
message StreamError {
	int32 grpc_code = 1;
	int32 http_code = 2;
	string message = 3;
	string http_status = 4;
	repeated google.protobuf.Any details = 5;
}
